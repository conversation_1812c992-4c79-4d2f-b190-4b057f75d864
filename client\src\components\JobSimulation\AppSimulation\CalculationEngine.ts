// Dynamic Calculation Engine for Job Simulation Components
// This engine allows for configurable calculations based on JSON rules
// Generated by AI

export interface CalculationRule {
  id: string;
  type: 'formula' | 'range' | 'conditional' | 'lookup';
  inputs: string[]; // dataIds that this rule depends on
  formula?: string; // JavaScript expression for formula type
  baseValue?: number;
  multipliers?: Record<string, CalculationMultiplier>;
  conditions?: CalculationCondition[];
  lookupTable?: Record<string, any>;
  formatters?: CalculationFormatter[];
}

export interface CalculationMultiplier {
  type: 'linear' | 'array_length' | 'range_width' | 'conditional';
  factor?: number;
  conditions?: Array<{
    condition: string; // JavaScript expression
    value: number;
  }>;
  maxRange?: number;
  minValue?: number;
}

export interface CalculationCondition {
  condition: string; // JavaScript expression
  value: any;
}

export interface CalculationFormatter {
  type: 'number' | 'currency' | 'range' | 'age-range' | 'percentage' | 'custom';
  template?: string; // For custom formatting like "$X.XX a day x 7 days"
  suffix?: string;
  precision?: number;
  // Range-specific options
  maxValue?: number; // For range formatting (e.g., 65 for age)
  maxLabel?: string; // Label when max value is reached (e.g., "65+")
  separator?: string; // Separator between min and max (default: " - ")
}

export interface CalculationConfig {
  rules: Record<string, CalculationRule>;
  dependencies: Record<string, string[]>; // Which rules depend on which inputs
  defaultValues?: Record<string, any>; // Default values for required inputs
}

// Helper functions for formatting
export const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(num % 1000000000 === 0 ? 0 : 2) + 'B';
  } else if (num >= 1000000) {
    return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 2) + 'M';
  } else if (num >= 1000) {
    const thousands = num / 1000;
    if (thousands >= 10) {
      return Math.round(thousands) + 'K';
    } else {
      return thousands.toFixed(1) + 'K';
    }
  }
  return num.toString();
};

export const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(2)} USD`;
};

export const formatPercentage = (value: number): string => {
  return `${value}%`;
};

export const formatRange = (
  value: [number, number],
  options?: { maxValue?: number; maxLabel?: string; separator?: string },
): string => {
  const separator = options?.separator || ' - ';
  const maxValue = options?.maxValue;
  const maxLabel = options?.maxLabel;

  if (maxValue !== undefined && maxLabel && value[1] >= maxValue) {
    return `${value[0]}${separator}${maxLabel}`;
  }

  return `${value[0]}${separator}${value[1]}`;
};

// Dynamic Calculation Engine
export class CalculationEngine {
  private config: CalculationConfig;
  private values: Record<string, any> = {};
  private calculatedValues: Record<string, any> = {};

  constructor(config: CalculationConfig) {
    this.config = config;
  }

  // Update input values and trigger recalculation
  updateValues(newValues: Record<string, any>) {
    this.values = { ...this.values, ...newValues };
    this.recalculate();
  }

  // Get calculated value by rule ID
  getValue(ruleId: string): any {
    return this.calculatedValues[ruleId];
  }

  // Get all calculated values
  getAllValues(): Record<string, any> {
    return { ...this.calculatedValues };
  }

  // Get all required input IDs from the configuration dependencies
  getRequiredInputs(): string[] {
    // Use dependencies keys as the source of truth for required inputs
    return Object.keys(this.config.dependencies);
  }

  // Get default values for required inputs from config or fallback logic
  getDefaultValues(): Record<string, any> {
    const defaults: Record<string, any> = {};
    const requiredInputs = this.getRequiredInputs();

    // First, use defaultValues from config if available
    if (this.config.defaultValues) {
      requiredInputs.forEach((inputId) => {
        if (this.config.defaultValues!.hasOwnProperty(inputId)) {
          defaults[inputId] = this.config.defaultValues![inputId];
        }
      });
    }

    // For any missing defaults, try to infer from rule multiplier types
    requiredInputs.forEach((inputId) => {
      if (!defaults.hasOwnProperty(inputId)) {
        const rulesUsingInput = Object.values(this.config.rules).filter((rule) =>
          rule.inputs.includes(inputId),
        );

        if (rulesUsingInput.length > 0) {
          const firstRule = rulesUsingInput[0];
          if (firstRule.multipliers?.[inputId]) {
            const multiplier = firstRule.multipliers[inputId];
            switch (multiplier.type) {
              case 'array_length':
                defaults[inputId] = [];
                break;
              case 'range_width':
                defaults[inputId] = [0, 100];
                break;
              case 'linear':
                defaults[inputId] = 1;
                break;
              case 'conditional':
                defaults[inputId] = '';
                break;
              default:
                defaults[inputId] = 0;
            }
          } else {
            defaults[inputId] = 0;
          }
        } else {
          defaults[inputId] = 0;
        }
      }
    });

    return defaults;
  }

  private recalculate() {
    // Clear previous calculations
    this.calculatedValues = {};

    // Calculate rules in dependency order with timeout protection
    const processedRules = new Set<string>();
    const ruleIds = Object.keys(this.config.rules);
    const maxIterations = ruleIds.length * 10; // Prevent infinite loops
    let iterations = 0;

    // Track rules that cannot be processed due to missing dependencies
    const unprocessableRules = new Set<string>();

    while (processedRules.size < ruleIds.length && iterations < maxIterations) {
      iterations++;
      let progressMade = false;

      for (const ruleId of ruleIds) {
        if (processedRules.has(ruleId) || unprocessableRules.has(ruleId)) continue;

        const rule = this.config.rules[ruleId];

        // Check if all dependencies are satisfied
        const canProcess = rule.inputs.every(
          (inputId) =>
            this.values.hasOwnProperty(inputId) || this.calculatedValues.hasOwnProperty(inputId),
        );

        if (canProcess) {
          try {
            this.calculatedValues[ruleId] = this.calculateRule(rule);
            processedRules.add(ruleId);
            progressMade = true;
          } catch (error) {
            console.error(`Error calculating rule ${ruleId}:`, error);
            unprocessableRules.add(ruleId);
          }
        } else {
          // Check if this rule can ever be processed with current inputs
          const hasRequiredInputs = rule.inputs.some((inputId) =>
            this.values.hasOwnProperty(inputId),
          );

          if (!hasRequiredInputs && iterations > ruleIds.length) {
            // Mark as unprocessable if no required inputs are available after several iterations
            unprocessableRules.add(ruleId);
            console.warn(
              `Rule ${ruleId} cannot be processed: missing required inputs [${rule.inputs.join(', ')}]`,
            );
          }
        }
      }

      // If no progress was made in this iteration, break to prevent infinite loop
      if (!progressMade && iterations > 2) {
        console.warn(
          'No progress made in calculation iteration, stopping to prevent infinite loop',
        );
        break;
      }
    }

    if (iterations >= maxIterations) {
      console.error('Calculation engine reached maximum iterations, possible circular dependency');
    }

    // Log summary
    if (processedRules.size < ruleIds.length) {
      const unprocessed = ruleIds.filter((id) => !processedRules.has(id));
      console.warn(`Could not process ${unprocessed.length} rules: [${unprocessed.join(', ')}]`);
    }
  }

  private calculateRule(rule: CalculationRule): any {
    switch (rule.type) {
      case 'formula':
        return this.calculateFormula(rule);
      case 'range':
        return this.calculateRange(rule);
      case 'conditional':
        return this.calculateConditional(rule);
      case 'lookup':
        return this.calculateLookup(rule);
      default:
        return null;
    }
  }

  private calculateFormula(rule: CalculationRule): any {
    if (!rule.formula) return null;

    try {
      // Create context with current values
      const context = { ...this.values, ...this.calculatedValues };

      // Simple formula evaluation (in production, use a safer evaluator)
      const result = this.evaluateFormula(rule.formula, context);

      return this.applyFormatters(result, rule.formatters);
    } catch (error) {
      console.error('Formula calculation error:', error);
      return null;
    }
  }

  private calculateRange(rule: CalculationRule): any {
    if (rule.baseValue === undefined) return null;

    let multiplier = 1;

    // Apply multipliers
    if (rule.multipliers) {
      for (const [inputId, multiplierConfig] of Object.entries(rule.multipliers)) {
        const inputValue = this.values[inputId] || this.calculatedValues[inputId];
        if (inputValue !== undefined) {
          multiplier *= this.calculateMultiplier(inputValue, multiplierConfig);
        }
      }
    }

    const minValue = Math.round(rule.baseValue * multiplier);
    const maxValue = Math.round(minValue * 2.8); // Default range multiplier

    const result = { min: minValue, max: maxValue };
    return this.applyFormatters(result, rule.formatters);
  }

  private calculateConditional(rule: CalculationRule): any {
    if (!rule.conditions) return null;

    const context = { ...this.values, ...this.calculatedValues };

    for (const condition of rule.conditions) {
      if (this.evaluateCondition(condition.condition, context)) {
        return this.applyFormatters(condition.value, rule.formatters);
      }
    }

    return null;
  }

  private calculateLookup(rule: CalculationRule): any {
    if (!rule.lookupTable || rule.inputs.length === 0) return null;

    const key = this.values[rule.inputs[0]] || this.calculatedValues[rule.inputs[0]];
    const result = rule.lookupTable[key];

    return this.applyFormatters(result, rule.formatters);
  }

  private calculateMultiplier(value: any, config: CalculationMultiplier): number {
    switch (config.type) {
      case 'linear':
        return (config.factor || 1) * (typeof value === 'number' ? value : 1);

      case 'array_length':
        const length = Array.isArray(value) ? value.length : 1;
        return Math.max(1, length * (config.factor || 1));

      case 'range_width':
        if (Array.isArray(value) && value.length >= 2) {
          const width = value[1] - value[0];
          const maxRange = config.maxRange || 100;
          return Math.max(config.minValue || 0.6, width / maxRange);
        }
        return 1;

      case 'conditional':
        if (config.conditions) {
          const context = { value, ...this.values, ...this.calculatedValues };
          for (const condition of config.conditions) {
            if (this.evaluateCondition(condition.condition, context)) {
              return condition.value;
            }
          }
        }
        return 1;

      default:
        return 1;
    }
  }

  private applyFormatters(value: any, formatters?: CalculationFormatter[]): any {
    if (!formatters || formatters.length === 0) return value;

    let result = value;

    for (const formatter of formatters) {
      switch (formatter.type) {
        case 'number':
          if (typeof result === 'number') {
            result = formatNumber(result);
          } else if (result && typeof result === 'object' && 'min' in result && 'max' in result) {
            result = `${formatNumber(result.min)} - ${formatNumber(result.max)}`;
          }
          break;

        case 'currency':
          if (typeof result === 'number') {
            result = formatCurrency(result);
          }
          break;

        case 'percentage':
          if (typeof result === 'number') {
            result = formatPercentage(result);
          }
          break;

        case 'range':
          if (Array.isArray(result) && result.length >= 2) {
            result = formatRange([result[0], result[1]], {
              maxValue: formatter.maxValue,
              maxLabel: formatter.maxLabel,
              separator: formatter.separator,
            });
          }
          break;

        case 'age-range':
          if (Array.isArray(result) && result.length >= 2) {
            result = formatRange([result[0], result[1]], {
              maxValue: formatter.maxValue || 65,
              maxLabel: formatter.maxLabel || '65+',
              separator: formatter.separator,
            });
          }
          break;

        case 'custom':
          if (formatter.template && typeof result === 'number') {
            result = formatter.template.replace(/\{value\}/g, result.toString());
          }
          break;
      }
    }

    return result;
  }

  private evaluateFormula(formula: string, context: Record<string, any>): any {
    // Simple formula evaluator - replace with a proper expression evaluator in production
    let expression = formula;

    // Replace variables with values
    for (const [key, value] of Object.entries(context)) {
      const regex = new RegExp(`\\b${key}\\b`, 'g');
      expression = expression.replace(regex, JSON.stringify(value));
    }

    // Evaluate basic mathematical expressions
    try {
      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      console.error('Formula evaluation error:', error);
      return 0;
    }
  }

  private evaluateCondition(condition: string, context: Record<string, any>): boolean {
    try {
      let expression = condition;

      // Replace variables with values
      for (const [key, value] of Object.entries(context)) {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        expression = expression.replace(regex, JSON.stringify(value));
      }

      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      console.error('Condition evaluation error:', error);
      return false;
    }
  }
}
